{"name": "neumorsweeper", "private": true, "version": "1.0.0", "type": "module", "description": "A neumorphic style Minesweeper game built with Svelte", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "devDependencies": {"@sveltejs/vite-plugin-svelte": "^5.0.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/svelte": "^5.2.8", "jsdom": "^26.1.0", "svelte": "^5.28.1", "vite": "^6.3.5", "vitest": "^3.1.4"}, "dependencies": {"@tailwindcss/vite": "^4.1.8", "tailwindcss": "^4.1.8"}}