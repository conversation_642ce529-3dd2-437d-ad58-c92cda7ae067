@import "tailwindcss";

/* CSS Variables for theming */
:root {
  /* Light theme colors - matching the designs exactly */
  --bg-primary: #e6e7ee;
  --bg-secondary: #e6e7ee;
  --text-primary: #8b8d98;
  --text-secondary: #a0a3bd;
  --text-accent: #6c6f7f;
  --shadow-light: #ffffff;
  --shadow-dark: #d1d9e6;
  --border-color: #cbd5e0;

  /* Game colors - matching the design */
  --mine-color: #ff6b6b;
  --flag-color: #ff6b6b;
  --number-1: #4dabf7;
  --number-2: #51cf66;
  --number-3: #ffd43b;
  --number-4: #9775fa;
  --number-5: #ff8cc8;
  --number-6: #22b8cf;
  --number-7: #ff922b;
  --number-8: #495057;
}

[data-theme="dark"] {
  /* Dark theme colors - matching the dark design */
  --bg-primary: #3c4043;
  --bg-secondary: #3c4043;
  --text-primary: #9aa0a6;
  --text-secondary: #80868b;
  --text-accent: #bdc1c6;
  --shadow-light: #5f6368;
  --shadow-dark: #202124;
  --border-color: #5f6368;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  min-height: 100vh;
  transition: background-color 0.3s ease, color 0.3s ease;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

/* Neumorphic button styles */
.neu-button {
  background: var(--bg-primary);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  color: var(--text-primary);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 6px 6px 12px var(--shadow-dark),
    -6px -6px 12px var(--shadow-light);
}

.neu-button:hover {
  box-shadow: 4px 4px 8px var(--shadow-dark), -4px -4px 8px var(--shadow-light);
}

.neu-button:active {
  box-shadow: inset 2px 2px 4px var(--shadow-dark),
    inset -2px -2px 4px var(--shadow-light);
}

/* Neumorphic inset styles */
.neu-inset {
  background: var(--bg-primary);
  border-radius: 12px;
  box-shadow: inset 4px 4px 8px var(--shadow-dark),
    inset -4px -4px 8px var(--shadow-light);
}

/* Neumorphic outset styles */
.neu-outset {
  background: var(--bg-primary);
  border-radius: 12px;
  box-shadow: 6px 6px 12px var(--shadow-dark),
    -6px -6px 12px var(--shadow-light);
}

/* Number colors for minesweeper */
.number-1 {
  color: var(--number-1);
}
.number-2 {
  color: var(--number-2);
}
.number-3 {
  color: var(--number-3);
}
.number-4 {
  color: var(--number-4);
}
.number-5 {
  color: var(--number-5);
}
.number-6 {
  color: var(--number-6);
}
.number-7 {
  color: var(--number-7);
}
.number-8 {
  color: var(--number-8);
}

/* Responsive design */
@media (max-width: 768px) {
  #app {
    padding: 0.5rem;
  }

  .neu-button {
    padding: 10px 20px;
    font-size: 14px;
  }
}
