/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Light theme colors */
  --color-primary: #f5f5f0;
  --color-secondary: #e8e8e0;
  --color-accent: #ff6b35;
  --color-text: #2c2c2c;
  --color-text-secondary: #666666;
  --color-shadow: rgba(0, 0, 0, 0.1);
  --color-shadow-inset: rgba(255, 255, 255, 0.8);
  --color-mine: #ff4444;
  --color-flag: #ff6b35;

  /* Typography */
  font-family: "SF Pro Display", -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* Rendering optimizations */
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

/* Dark theme */
[data-theme="dark"] {
  --color-primary: #1a1a1a;
  --color-secondary: #2d2d2d;
  --color-accent: #ff6b35;
  --color-text: #ffffff;
  --color-text-secondary: #cccccc;
  --color-shadow: rgba(0, 0, 0, 0.3);
  --color-shadow-inset: rgba(255, 255, 255, 0.1);
  --color-mine: #ff4444;
  --color-flag: #ff6b35;
}

html {
  height: 100%;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  min-height: 100vh;
  background: var(--color-primary);
  color: var(--color-text);
  transition: background-color 0.2s ease, color 0.2s ease;
  overflow-x: hidden;
}

#app {
  width: 100%;
  min-height: 100vh;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "SF Pro Display", -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, sans-serif;
  font-weight: 700;
  line-height: 1.2;
  color: var(--color-text);
}

p {
  color: var(--color-text-secondary);
  line-height: 1.6;
}

/* Links */
a {
  color: var(--color-accent);
  text-decoration: none;
  transition: color 0.15s ease;
}

a:hover {
  color: var(--color-accent);
  opacity: 0.8;
}

/* Focus styles for accessibility */
button:focus-visible,
a:focus-visible,
input:focus-visible {
  outline: 2px solid var(--color-accent);
  outline-offset: 2px;
}

/* Disable text selection on game elements */
.no-select {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Utility classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Responsive container */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 10px;
  }
}

/* Prevent zoom on mobile inputs */
@media screen and (max-width: 768px) {
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  select,
  textarea {
    font-size: 16px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --color-shadow: rgba(0, 0, 0, 0.3);
    --color-shadow-inset: rgba(255, 255, 255, 1);
  }

  [data-theme="dark"] {
    --color-shadow: rgba(0, 0, 0, 0.6);
    --color-shadow-inset: rgba(255, 255, 255, 0.3);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Print styles */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a,
  a:visited {
    text-decoration: underline;
  }

  .settings-overlay,
  .settings-modal {
    display: none !important;
  }
}
