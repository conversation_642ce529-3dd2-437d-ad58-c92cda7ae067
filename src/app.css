@import "tailwindcss";

/* CSS Variables for theming */
:root {
  /* Light theme colors */
  --bg-primary: #e6e7ee;
  --bg-secondary: #f0f1f8;
  --text-primary: #4a5568;
  --text-secondary: #718096;
  --text-accent: #2d3748;
  --shadow-light: #ffffff;
  --shadow-dark: #d1d9e6;
  --border-color: #cbd5e0;

  /* Game colors */
  --mine-color: #e53e3e;
  --flag-color: #e53e3e;
  --number-1: #2b6cb0;
  --number-2: #38a169;
  --number-3: #d69e2e;
  --number-4: #805ad5;
  --number-5: #d53f8c;
  --number-6: #00b5d8;
  --number-7: #dd6b20;
  --number-8: #2d3748;
}

[data-theme="dark"] {
  /* Dark theme colors */
  --bg-primary: #2d3748;
  --bg-secondary: #4a5568;
  --text-primary: #e2e8f0;
  --text-secondary: #a0aec0;
  --text-accent: #f7fafc;
  --shadow-light: #4a5568;
  --shadow-dark: #1a202c;
  --border-color: #4a5568;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  min-height: 100vh;
  transition: background-color 0.3s ease, color 0.3s ease;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

/* Neumorphic button styles */
.neu-button {
  background: var(--bg-primary);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  color: var(--text-primary);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 6px 6px 12px var(--shadow-dark),
    -6px -6px 12px var(--shadow-light);
}

.neu-button:hover {
  box-shadow: 4px 4px 8px var(--shadow-dark), -4px -4px 8px var(--shadow-light);
}

.neu-button:active {
  box-shadow: inset 2px 2px 4px var(--shadow-dark),
    inset -2px -2px 4px var(--shadow-light);
}

/* Neumorphic inset styles */
.neu-inset {
  background: var(--bg-primary);
  border-radius: 12px;
  box-shadow: inset 4px 4px 8px var(--shadow-dark),
    inset -4px -4px 8px var(--shadow-light);
}

/* Neumorphic outset styles */
.neu-outset {
  background: var(--bg-primary);
  border-radius: 12px;
  box-shadow: 6px 6px 12px var(--shadow-dark),
    -6px -6px 12px var(--shadow-light);
}

/* Number colors for minesweeper */
.number-1 {
  color: var(--number-1);
}
.number-2 {
  color: var(--number-2);
}
.number-3 {
  color: var(--number-3);
}
.number-4 {
  color: var(--number-4);
}
.number-5 {
  color: var(--number-5);
}
.number-6 {
  color: var(--number-6);
}
.number-7 {
  color: var(--number-7);
}
.number-8 {
  color: var(--number-8);
}

/* Responsive design */
@media (max-width: 768px) {
  #app {
    padding: 0.5rem;
  }

  .neu-button {
    padding: 10px 20px;
    font-size: 14px;
  }
}
