import { CELL_STATES, CELL_TYPES, GAME_STATES } from './constants.js';

/**
 * Creates a new game board with the specified dimensions
 * @param {number} width - Board width
 * @param {number} height - Board height
 * @returns {Array} 2D array representing the game board
 */
export function createBoard(width, height) {
  const board = [];
  for (let row = 0; row < height; row++) {
    board[row] = [];
    for (let col = 0; col < width; col++) {
      board[row][col] = {
        type: CELL_TYPES.EMPTY,
        state: CELL_STATES.HIDDEN,
        adjacentMines: 0,
        row,
        col
      };
    }
  }
  return board;
}

/**
 * Places mines randomly on the board, avoiding the first clicked cell
 * @param {Array} board - The game board
 * @param {number} mineCount - Number of mines to place
 * @param {number} firstClickRow - Row of first clicked cell
 * @param {number} firstClickCol - Column of first clicked cell
 * @returns {Array} Board with mines placed
 */
export function placeMines(board, mineCount, firstClickRow, firstClickCol) {
  const height = board.length;
  const width = board[0].length;
  const mines = new Set();
  
  // Get all possible positions except the first click and its neighbors
  const forbiddenPositions = new Set();
  for (let dr = -1; dr <= 1; dr++) {
    for (let dc = -1; dc <= 1; dc++) {
      const r = firstClickRow + dr;
      const c = firstClickCol + dc;
      if (r >= 0 && r < height && c >= 0 && c < width) {
        forbiddenPositions.add(`${r},${c}`);
      }
    }
  }
  
  // Place mines randomly
  while (mines.size < mineCount) {
    const row = Math.floor(Math.random() * height);
    const col = Math.floor(Math.random() * width);
    const position = `${row},${col}`;
    
    if (!mines.has(position) && !forbiddenPositions.has(position)) {
      mines.add(position);
      board[row][col].type = CELL_TYPES.MINE;
    }
  }
  
  // Calculate adjacent mine counts
  for (let row = 0; row < height; row++) {
    for (let col = 0; col < width; col++) {
      if (board[row][col].type !== CELL_TYPES.MINE) {
        board[row][col].adjacentMines = countAdjacentMines(board, row, col);
      }
    }
  }
  
  return board;
}

/**
 * Counts the number of mines adjacent to a cell
 * @param {Array} board - The game board
 * @param {number} row - Cell row
 * @param {number} col - Cell column
 * @returns {number} Number of adjacent mines
 */
export function countAdjacentMines(board, row, col) {
  const height = board.length;
  const width = board[0].length;
  let count = 0;
  
  for (let dr = -1; dr <= 1; dr++) {
    for (let dc = -1; dc <= 1; dc++) {
      if (dr === 0 && dc === 0) continue;
      
      const r = row + dr;
      const c = col + dc;
      
      if (r >= 0 && r < height && c >= 0 && c < width) {
        if (board[r][c].type === CELL_TYPES.MINE) {
          count++;
        }
      }
    }
  }
  
  return count;
}

/**
 * Reveals a cell and potentially adjacent cells
 * @param {Array} board - The game board
 * @param {number} row - Cell row
 * @param {number} col - Cell column
 * @returns {Object} Result with updated board and revealed cells count
 */
export function revealCell(board, row, col) {
  const height = board.length;
  const width = board[0].length;
  let revealedCount = 0;
  
  if (row < 0 || row >= height || col < 0 || col >= width) {
    return { board, revealedCount };
  }
  
  const cell = board[row][col];
  
  if (cell.state !== CELL_STATES.HIDDEN) {
    return { board, revealedCount };
  }
  
  // Reveal the cell
  cell.state = CELL_STATES.REVEALED;
  revealedCount++;
  
  // If it's an empty cell with no adjacent mines, reveal neighbors
  if (cell.type === CELL_TYPES.EMPTY && cell.adjacentMines === 0) {
    for (let dr = -1; dr <= 1; dr++) {
      for (let dc = -1; dc <= 1; dc++) {
        if (dr === 0 && dc === 0) continue;
        
        const result = revealCell(board, row + dr, col + dc);
        revealedCount += result.revealedCount;
      }
    }
  }
  
  return { board, revealedCount };
}

/**
 * Toggles flag state of a cell
 * @param {Array} board - The game board
 * @param {number} row - Cell row
 * @param {number} col - Cell column
 * @returns {Array} Updated board
 */
export function toggleFlag(board, row, col) {
  const cell = board[row][col];
  
  if (cell.state === CELL_STATES.REVEALED) {
    return board;
  }
  
  if (cell.state === CELL_STATES.HIDDEN) {
    cell.state = CELL_STATES.FLAGGED;
  } else if (cell.state === CELL_STATES.FLAGGED) {
    cell.state = CELL_STATES.HIDDEN;
  }
  
  return board;
}

/**
 * Checks if the game is won
 * @param {Array} board - The game board
 * @param {number} totalMines - Total number of mines
 * @returns {boolean} True if game is won
 */
export function checkWin(board, totalMines) {
  const height = board.length;
  const width = board[0].length;
  const totalCells = height * width;
  let revealedCells = 0;
  
  for (let row = 0; row < height; row++) {
    for (let col = 0; col < width; col++) {
      if (board[row][col].state === CELL_STATES.REVEALED) {
        revealedCells++;
      }
    }
  }
  
  return revealedCells === totalCells - totalMines;
}

/**
 * Reveals all mines on the board (for game over)
 * @param {Array} board - The game board
 * @returns {Array} Updated board with all mines revealed
 */
export function revealAllMines(board) {
  const height = board.length;
  const width = board[0].length;
  
  for (let row = 0; row < height; row++) {
    for (let col = 0; col < width; col++) {
      const cell = board[row][col];
      if (cell.type === CELL_TYPES.MINE && cell.state !== CELL_STATES.FLAGGED) {
        cell.state = CELL_STATES.REVEALED;
      }
    }
  }
  
  return board;
}

/**
 * Counts the number of flagged cells
 * @param {Array} board - The game board
 * @returns {number} Number of flagged cells
 */
export function countFlags(board) {
  const height = board.length;
  const width = board[0].length;
  let flagCount = 0;
  
  for (let row = 0; row < height; row++) {
    for (let col = 0; col < width; col++) {
      if (board[row][col].state === CELL_STATES.FLAGGED) {
        flagCount++;
      }
    }
  }
  
  return flagCount;
}
