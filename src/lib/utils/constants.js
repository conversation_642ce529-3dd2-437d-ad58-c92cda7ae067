// Game difficulty configurations
export const DIFFICULTIES = {
  EASY: {
    name: 'Easy',
    width: 10,
    height: 10,
    mines: 10,
    description: '10 x 10, 10 mines'
  },
  MEDIUM: {
    name: 'Medium',
    width: 16,
    height: 16,
    mines: 40,
    description: '16 x 16, 40 mines'
  },
  HARD: {
    name: 'Hard',
    width: 30,
    height: 16,
    mines: 99,
    description: '30 x 16, 99 mines'
  }
};

// Cell states
export const CELL_STATES = {
  HIDDEN: 'hidden',
  REVEALED: 'revealed',
  FLAGGED: 'flagged',
  QUESTIONED: 'questioned'
};

// Game states
export const GAME_STATES = {
  READY: 'ready',
  PLAYING: 'playing',
  WON: 'won',
  LOST: 'lost'
};

// Cell types
export const CELL_TYPES = {
  EMPTY: 0,
  MINE: -1
};

// Theme modes
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark'
};

// Animation durations (in milliseconds)
export const ANIMATIONS = {
  CELL_REVEAL: 150,
  GAME_OVER: 300,
  THEME_TRANSITION: 200
};

// Local storage keys
export const STORAGE_KEYS = {
  THEME: 'neumorsweeper-theme',
  DIFFICULTY: 'neumorsweeper-difficulty',
  BEST_TIMES: 'neumorsweeper-best-times'
};

// Color palette
export const COLORS = {
  LIGHT: {
    primary: '#f5f5f0',
    secondary: '#e8e8e0',
    accent: '#ff6b35',
    text: '#2c2c2c',
    textSecondary: '#666666',
    shadow: 'rgba(0, 0, 0, 0.1)',
    shadowInset: 'rgba(255, 255, 255, 0.8)',
    mine: '#ff4444',
    flag: '#ff6b35',
    numbers: {
      1: '#2196f3',
      2: '#4caf50',
      3: '#ff6b35',
      4: '#9c27b0',
      5: '#795548',
      6: '#607d8b',
      7: '#000000',
      8: '#424242'
    }
  },
  DARK: {
    primary: '#1a1a1a',
    secondary: '#2d2d2d',
    accent: '#ff6b35',
    text: '#ffffff',
    textSecondary: '#cccccc',
    shadow: 'rgba(0, 0, 0, 0.3)',
    shadowInset: 'rgba(255, 255, 255, 0.1)',
    mine: '#ff4444',
    flag: '#ff6b35',
    numbers: {
      1: '#64b5f6',
      2: '#81c784',
      3: '#ff6b35',
      4: '#ba68c8',
      5: '#a1887f',
      6: '#90a4ae',
      7: '#ffffff',
      8: '#bdbdbd'
    }
  }
};

// Responsive breakpoints
export const BREAKPOINTS = {
  MOBILE: 480,
  TABLET: 768,
  DESKTOP: 1024
};
