<script>
  import { createEventDispatcher } from "svelte";

  export let cell;
  export let gameState;

  const dispatch = createEventDispatcher();

  function handleClick() {
    if (gameState === "lost" || gameState === "won") return;
    if (cell.isRevealed || cell.isFlagged) return;

    dispatch("reveal", { row: cell.row, col: cell.col });
  }

  function handleRightClick(event) {
    event.preventDefault();
    if (gameState === "lost" || gameState === "won") return;
    if (cell.isRevealed) return;

    dispatch("flag", { row: cell.row, col: cell.col });
  }

  function getCellContent() {
    if (cell.isFlagged && gameState !== "lost") {
      return "🚩";
    }

    // Show all mines when game is lost
    if (gameState === "lost" && cell.isMine) {
      return "💣";
    }

    if (!cell.isRevealed) {
      return "";
    }

    if (cell.isMine) {
      return "💣";
    }

    if (cell.neighborMines > 0) {
      return cell.neighborMines.toString();
    }

    return "";
  }

  function getCellClass() {
    let classes = ["cell"];

    // Show mines as revealed when game is lost
    if (cell.isRevealed || (gameState === "lost" && cell.isMine)) {
      classes.push("revealed");
      if (cell.isMine) {
        classes.push("mine");
      } else if (cell.neighborMines > 0) {
        classes.push(`number-${cell.neighborMines}`);
      }
    } else {
      classes.push("hidden");
      if (cell.isFlagged && gameState !== "lost") {
        classes.push("flagged");
      }
    }

    return classes.join(" ");
  }
</script>

<button
  class={getCellClass()}
  on:click={handleClick}
  on:contextmenu={handleRightClick}
  disabled={gameState === "lost" || gameState === "won"}
>
  {getCellContent()}
</button>

<style>
  .cell {
    width: 32px;
    height: 32px;
    border: none;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.1s ease;
    user-select: none;
    background: var(--bg-primary);
  }

  .cell.hidden {
    border-radius: 6px;
    box-shadow:
      3px 3px 6px var(--shadow-dark),
      -3px -3px 6px var(--shadow-light);
  }

  .cell.hidden:hover:not(:disabled) {
    box-shadow:
      2px 2px 4px var(--shadow-dark),
      -2px -2px 4px var(--shadow-light);
  }

  .cell.hidden:active:not(:disabled) {
    box-shadow:
      inset 1px 1px 2px var(--shadow-dark),
      inset -1px -1px 2px var(--shadow-light);
  }

  .cell.revealed {
    border-radius: 4px;
    box-shadow:
      inset 2px 2px 4px var(--shadow-dark),
      inset -2px -2px 4px var(--shadow-light);
    background: var(--bg-secondary);
  }

  .cell.mine {
    background: var(--mine-color);
    color: white;
  }

  .cell.flagged {
    background: var(--bg-primary);
  }

  .cell:disabled {
    cursor: default;
  }

  /* Responsive sizing */
  @media (max-width: 768px) {
    .cell {
      width: 28px;
      height: 28px;
      font-size: 12px;
    }
  }

  @media (max-width: 480px) {
    .cell {
      width: 24px;
      height: 24px;
      font-size: 10px;
    }
  }
</style>
