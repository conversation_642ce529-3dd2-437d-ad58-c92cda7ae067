import { writable, derived } from 'svelte/store';

// Game difficulty settings
export const difficulties = {
  easy: { width: 10, height: 10, mines: 10 },
  medium: { width: 16, height: 16, mines: 40 },
  hard: { width: 30, height: 16, mines: 99 }
};

// Theme store
export const isDarkTheme = writable(false);

// Game settings
export const currentDifficulty = writable('easy');

// Game state
export const gameState = writable('ready'); // 'ready', 'playing', 'won', 'lost'
export const gameBoard = writable([]);
export const mineCount = writable(0);
export const flagCount = writable(0);
export const revealedCount = writable(0);
export const gameTime = writable(0);
export const isFirstClick = writable(true);

// Settings modal
export const showSettings = writable(false);

// Derived stores
export const remainingMines = derived(
  [mineCount, flagCount],
  ([$mineCount, $flagCount]) => $mineCount - $flagCount
);

export const isGameWon = derived(
  [gameState],
  ([$gameState]) => $gameState === 'won'
);

export const isGameLost = derived(
  [gameState],
  ([$gameState]) => $gameState === 'lost'
);

export const isGameActive = derived(
  [gameState],
  ([$gameState]) => $gameState === 'playing'
);
