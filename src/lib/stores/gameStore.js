import { writable, derived } from 'svelte/store';
import { 
  DIFFICULTIES, 
  GAME_STATES, 
  CELL_STATES, 
  CELL_TYPES, 
  THEMES,
  STORAGE_KEYS 
} from '../utils/constants.js';
import {
  createBoard,
  placeMines,
  revealCell,
  toggleFlag,
  checkWin,
  revealAllMines,
  countFlags
} from '../utils/gameLogic.js';

// Theme store
function createThemeStore() {
  const savedTheme = typeof window !== 'undefined' 
    ? localStorage.getItem(STORAGE_KEYS.THEME) || THEMES.LIGHT
    : THEMES.LIGHT;
  
  const { subscribe, set, update } = writable(savedTheme);
  
  return {
    subscribe,
    toggle: () => update(theme => {
      const newTheme = theme === THEMES.LIGHT ? THEMES.DARK : THEMES.LIGHT;
      if (typeof window !== 'undefined') {
        localStorage.setItem(STORAGE_KEYS.THEME, newTheme);
      }
      return newTheme;
    }),
    set
  };
}

// Game configuration store
function createConfigStore() {
  const savedDifficulty = typeof window !== 'undefined'
    ? localStorage.getItem(STORAGE_KEYS.DIFFICULTY) || 'EASY'
    : 'EASY';
  
  const { subscribe, set, update } = writable({
    difficulty: savedDifficulty,
    ...DIFFICULTIES[savedDifficulty]
  });
  
  return {
    subscribe,
    setDifficulty: (difficulty) => {
      if (typeof window !== 'undefined') {
        localStorage.setItem(STORAGE_KEYS.DIFFICULTY, difficulty);
      }
      set({
        difficulty,
        ...DIFFICULTIES[difficulty]
      });
    },
    set,
    update
  };
}

// Game state store
function createGameStore() {
  const initialState = {
    board: [],
    gameState: GAME_STATES.READY,
    startTime: null,
    endTime: null,
    firstClick: true,
    minesRemaining: 0,
    revealedCells: 0
  };
  
  const { subscribe, set, update } = writable(initialState);
  
  return {
    subscribe,
    
    // Initialize new game
    newGame: (config) => {
      const board = createBoard(config.width, config.height);
      set({
        board,
        gameState: GAME_STATES.READY,
        startTime: null,
        endTime: null,
        firstClick: true,
        minesRemaining: config.mines,
        revealedCells: 0
      });
    },
    
    // Handle cell click
    cellClick: (row, col) => {
      update(state => {
        if (state.gameState === GAME_STATES.WON || state.gameState === GAME_STATES.LOST) {
          return state;
        }
        
        const cell = state.board[row][col];
        
        // Can't click on flagged or revealed cells
        if (cell.state === CELL_STATES.FLAGGED || cell.state === CELL_STATES.REVEALED) {
          return state;
        }
        
        // First click - place mines and start timer
        if (state.firstClick) {
          state.board = placeMines(state.board, state.minesRemaining, row, col);
          state.startTime = Date.now();
          state.gameState = GAME_STATES.PLAYING;
          state.firstClick = false;
        }
        
        // Reveal the cell
        const result = revealCell(state.board, row, col);
        state.board = result.board;
        state.revealedCells += result.revealedCount;
        
        // Check if clicked on mine
        if (cell.type === CELL_TYPES.MINE) {
          state.gameState = GAME_STATES.LOST;
          state.endTime = Date.now();
          state.board = revealAllMines(state.board);
        } else {
          // Check for win condition
          const config = get(gameConfig);
          if (checkWin(state.board, config.mines)) {
            state.gameState = GAME_STATES.WON;
            state.endTime = Date.now();
          }
        }
        
        return state;
      });
    },
    
    // Handle right click (flag)
    cellRightClick: (row, col) => {
      update(state => {
        if (state.gameState === GAME_STATES.WON || state.gameState === GAME_STATES.LOST) {
          return state;
        }
        
        state.board = toggleFlag(state.board, row, col);
        return state;
      });
    },
    
    // Reset game
    reset: () => {
      update(state => ({
        ...state,
        gameState: GAME_STATES.READY,
        startTime: null,
        endTime: null,
        firstClick: true,
        revealedCells: 0
      }));
    },
    
    set,
    update
  };
}

// Timer store
function createTimerStore() {
  const { subscribe, set, update } = writable(0);
  let interval = null;
  
  return {
    subscribe,
    start: () => {
      if (interval) clearInterval(interval);
      const startTime = Date.now();
      interval = setInterval(() => {
        set(Math.floor((Date.now() - startTime) / 1000));
      }, 1000);
    },
    stop: () => {
      if (interval) {
        clearInterval(interval);
        interval = null;
      }
    },
    reset: () => {
      if (interval) {
        clearInterval(interval);
        interval = null;
      }
      set(0);
    },
    set
  };
}

// Settings store
function createSettingsStore() {
  const { subscribe, set, update } = writable({
    showSettings: false,
    soundEnabled: true,
    animationsEnabled: true
  });
  
  return {
    subscribe,
    toggleSettings: () => update(s => ({ ...s, showSettings: !s.showSettings })),
    closeSettings: () => update(s => ({ ...s, showSettings: false })),
    toggleSound: () => update(s => ({ ...s, soundEnabled: !s.soundEnabled })),
    toggleAnimations: () => update(s => ({ ...s, animationsEnabled: !s.animationsEnabled })),
    set,
    update
  };
}

// Create store instances
export const theme = createThemeStore();
export const gameConfig = createConfigStore();
export const gameState = createGameStore();
export const timer = createTimerStore();
export const settings = createSettingsStore();

// Derived stores
export const minesRemaining = derived(
  gameState,
  $gameState => {
    const flagCount = countFlags($gameState.board);
    return Math.max(0, $gameState.minesRemaining - flagCount);
  }
);

export const gameTime = derived(
  [gameState, timer],
  ([$gameState, $timer]) => {
    if ($gameState.gameState === GAME_STATES.READY) {
      return 0;
    } else if ($gameState.gameState === GAME_STATES.PLAYING) {
      return $timer;
    } else if ($gameState.endTime && $gameState.startTime) {
      return Math.floor(($gameState.endTime - $gameState.startTime) / 1000);
    }
    return $timer;
  }
);

// Helper function to get current value of a store
function get(store) {
  let value;
  store.subscribe(v => value = v)();
  return value;
}
