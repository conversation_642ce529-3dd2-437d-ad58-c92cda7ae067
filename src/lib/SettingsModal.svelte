<script>
  import { createEventDispatcher } from "svelte";
  import { isDarkTheme, currentDifficulty, difficulties } from "./stores.js";

  export let isOpen = false;

  const dispatch = createEventDispatcher();

  function handleClose() {
    dispatch("close");
  }

  function handleDifficultyChange(difficulty) {
    currentDifficulty.set(difficulty);
    dispatch("difficulty-change", difficulty);
  }

  function handleThemeToggle() {
    isDarkTheme.update((value) => !value);
    dispatch("theme-toggle");
  }

  function handleOverlayClick(event) {
    if (event.target === event.currentTarget) {
      handleClose();
    }
  }

  function getDifficultyLabel(key) {
    const config = difficulties[key];
    const label = key.charAt(0).toUpperCase() + key.slice(1);
    return `${label} (${config.width} × ${config.height}, ${config.mines} mines)`;
  }
</script>

{#if isOpen}
  <div
    class="modal-overlay"
    on:click={handleOverlayClick}
    on:keydown={handleOverlayClick}
    role="button"
    tabindex="-1"
  >
    <div class="modal-content neu-outset">
      <div class="modal-header">
        <div class="modal-title">
          <span class="settings-icon">⚙️</span>
          <h2>Settings</h2>
        </div>
        <p class="modal-subtitle">Customize your game</p>
        <button class="close-btn neu-button" on:click={handleClose}> ✕ </button>
      </div>

      <div class="modal-body">
        <!-- Theme Toggle -->
        <div class="setting-group">
          <div class="setting-header">
            <h3>Dark theme</h3>
            <p>Enable dark theme for the game</p>
          </div>
          <div class="setting-control">
            <label class="toggle-switch">
              <input
                type="checkbox"
                checked={$isDarkTheme}
                on:change={handleThemeToggle}
              />
              <span class="toggle-slider"></span>
            </label>
          </div>
        </div>

        <!-- Difficulty Selection -->
        <div class="setting-group">
          <div class="setting-header">
            <h3>Difficulty</h3>
            <p>Customize the board size and mine count</p>
          </div>
          <div class="setting-control">
            <div class="difficulty-options">
              {#each Object.keys(difficulties) as difficulty}
                <button
                  class="difficulty-btn {$currentDifficulty === difficulty
                    ? 'active'
                    : ''}"
                  on:click={() => handleDifficultyChange(difficulty)}
                >
                  {getDifficultyLabel(difficulty)}
                </button>
              {/each}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{/if}

<style>
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 1rem;
  }

  .modal-content {
    background: var(--bg-primary);
    border-radius: 20px;
    padding: 2rem;
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
  }

  .modal-header {
    margin-bottom: 2rem;
    position: relative;
  }

  .modal-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
  }

  .settings-icon {
    font-size: 1.5rem;
  }

  .modal-title h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-accent);
  }

  .modal-subtitle {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.875rem;
  }

  .close-btn {
    position: absolute;
    top: 0;
    right: 0;
    width: 40px;
    height: 40px;
    border-radius: 10px;
    font-size: 1.125rem;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
  }

  .modal-body {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .setting-group {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;
  }

  .setting-header h3 {
    margin: 0 0 0.25rem 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-accent);
  }

  .setting-header p {
    margin: 0;
    font-size: 0.875rem;
    color: var(--text-secondary);
  }

  .setting-control {
    flex-shrink: 0;
  }

  /* Toggle Switch */
  .toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
  }

  .toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-primary);
    border-radius: 17px;
    transition: 0.3s;
    box-shadow:
      inset 3px 3px 6px var(--shadow-dark),
      inset -3px -3px 6px var(--shadow-light);
  }

  .toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background: var(--bg-primary);
    border-radius: 50%;
    transition: 0.3s;
    box-shadow:
      2px 2px 4px var(--shadow-dark),
      -2px -2px 4px var(--shadow-light);
  }

  input:checked + .toggle-slider:before {
    transform: translateX(26px);
    background: var(--number-2);
  }

  /* Difficulty Options */
  .difficulty-options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    min-width: 200px;
  }

  .difficulty-btn {
    background: var(--bg-primary);
    border: none;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
    box-shadow:
      4px 4px 8px var(--shadow-dark),
      -4px -4px 8px var(--shadow-light);
  }

  .difficulty-btn:hover {
    box-shadow:
      2px 2px 4px var(--shadow-dark),
      -2px -2px 4px var(--shadow-light);
  }

  .difficulty-btn.active {
    background: var(--number-2);
    color: white;
    box-shadow:
      inset 2px 2px 4px rgba(0, 0, 0, 0.2),
      inset -2px -2px 4px rgba(255, 255, 255, 0.1);
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .modal-content {
      padding: 1.5rem;
      margin: 1rem;
    }

    .setting-group {
      flex-direction: column;
      align-items: stretch;
      gap: 1rem;
    }

    .difficulty-options {
      min-width: auto;
    }
  }
</style>
