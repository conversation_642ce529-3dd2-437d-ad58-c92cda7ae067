<script>
  import { createEventDispatcher } from "svelte";
  import {
    remainingMines,
    gameTime,
    isDarkTheme,
    gameState,
  } from "./stores.js";

  const dispatch = createEventDispatcher();

  function formatTime(seconds) {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  }

  function formatMineCount(count) {
    return count.toString().padStart(2, "0");
  }

  function handleSettingsClick() {
    dispatch("settings");
  }

  function handleThemeToggle() {
    dispatch("theme-toggle");
  }

  function handleNewGame() {
    dispatch("new-game");
  }
</script>

<!-- Top Navigation Bar - exactly like in designs -->
<div class="top-nav">
  <div class="nav-left">
    <div class="logo-container">
      <span class="logo-icon">💣</span>
      <div class="logo-text">
        <span class="logo-title">Neumorsweeper</span>
        <span class="logo-subtitle">Neumorphic Minesweeper</span>
      </div>
    </div>
  </div>

  <div class="nav-right">
    <button class="nav-btn" on:click={handleSettingsClick} title="Settings">
      ⚙️
    </button>
    <button class="nav-btn" on:click={handleThemeToggle} title="Toggle Theme">
      {$isDarkTheme ? "☀️" : "🌙"}
    </button>
    <button class="nav-btn" title="GitHub"> 📁 </button>
  </div>
</div>

<!-- Game Stats Bar - exactly like in designs -->
<div class="stats-bar">
  <div class="stat-display mine-count">
    <span class="stat-icon">💣</span>
    <span class="stat-number">{formatMineCount($remainingMines)}</span>
  </div>

  <div class="stat-display timer">
    <span class="stat-icon">⏱️</span>
    <span class="stat-number">{formatTime($gameTime)}</span>
  </div>
</div>

<style>
  /* Top Navigation Bar */
  .top-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    z-index: 100;
    border-bottom: 1px solid var(--border-color);
  }

  .nav-left {
    display: flex;
    align-items: center;
  }

  .logo-container {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .logo-icon {
    font-size: 24px;
  }

  .logo-text {
    display: flex;
    flex-direction: column;
  }

  .logo-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-accent);
    line-height: 1;
  }

  .logo-subtitle {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1;
  }

  .nav-right {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .nav-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 16px;
    color: var(--text-secondary);
    transition: all 0.2s ease;
  }

  .nav-btn:hover {
    background: var(--bg-secondary);
    color: var(--text-accent);
  }

  /* Stats Bar */
  .stats-bar {
    position: fixed;
    top: 60px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 16px;
    z-index: 99;
    padding: 16px 0;
  }

  .stat-display {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow:
      inset 4px 4px 8px var(--shadow-dark),
      inset -4px -4px 8px var(--shadow-light);
    min-width: 80px;
    justify-content: center;
  }

  .stat-icon {
    font-size: 16px;
  }

  .stat-number {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-accent);
    font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", monospace;
  }

  /* Responsive */
  @media (max-width: 768px) {
    .top-nav {
      padding: 0 16px;
    }

    .logo-title {
      font-size: 14px;
    }

    .logo-subtitle {
      font-size: 11px;
    }

    .nav-btn {
      width: 28px;
      height: 28px;
      font-size: 14px;
    }

    .stats-bar {
      gap: 12px;
      padding: 12px 0;
    }

    .stat-display {
      padding: 6px 12px;
      min-width: 70px;
    }

    .stat-icon {
      font-size: 14px;
    }

    .stat-number {
      font-size: 14px;
    }
  }
</style>
