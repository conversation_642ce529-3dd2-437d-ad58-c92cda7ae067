<script>
  import { createEventDispatcher } from "svelte";
  import {
    remainingMines,
    gameTime,
    isDarkTheme,
    gameState,
  } from "./stores.js";

  const dispatch = createEventDispatcher();

  function formatTime(seconds) {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  }

  function formatMineCount(count) {
    return count.toString().padStart(2, "0");
  }

  function handleSettingsClick() {
    dispatch("settings");
  }

  function handleThemeToggle() {
    dispatch("theme-toggle");
  }

  function handleNewGame() {
    dispatch("new-game");
  }
</script>

<header class="header">
  <div class="header-content">
    <!-- Logo and Title -->
    <div class="logo-section">
      <div class="logo">💣</div>
      <h1 class="title">Neumorsweeper</h1>
      <p class="subtitle">Neumorphic Minesweeper</p>
    </div>

    <!-- Game Stats -->
    <div class="stats-section">
      <div class="stat-item neu-inset">
        <div class="stat-icon">💣</div>
        <div class="stat-value">{formatMineCount($remainingMines)}</div>
      </div>

      <div class="stat-item neu-inset">
        <div class="stat-icon">⏱️</div>
        <div class="stat-value">{formatTime($gameTime)}</div>
      </div>

      {#if $gameState === "won"}
        <div class="status-indicator won">🎉</div>
      {:else if $gameState === "lost"}
        <div class="status-indicator lost">💥</div>
      {/if}
    </div>

    <!-- Controls -->
    <div class="controls-section">
      <button
        class="control-btn neu-button"
        on:click={handleNewGame}
        title="New Game"
      >
        🔄
      </button>

      <button
        class="control-btn neu-button"
        on:click={handleThemeToggle}
        title="Toggle Theme"
      >
        {$isDarkTheme ? "☀️" : "🌙"}
      </button>

      <button
        class="control-btn neu-button"
        on:click={handleSettingsClick}
        title="Settings"
      >
        ⚙️
      </button>
    </div>
  </div>
</header>

<style>
  .header {
    width: 100%;
    max-width: 800px;
    margin-bottom: 2rem;
  }

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    background: var(--bg-primary);
    border-radius: 20px;
    box-shadow:
      8px 8px 16px var(--shadow-dark),
      -8px -8px 16px var(--shadow-light);
  }

  .logo-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .logo {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }

  .title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    color: var(--text-accent);
  }

  .subtitle {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
  }

  .stats-section {
    display: flex;
    gap: 1rem;
  }

  .stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    min-width: 80px;
  }

  .stat-icon {
    font-size: 1.25rem;
  }

  .stat-value {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-accent);
    font-family: "Courier New", monospace;
  }

  .controls-section {
    display: flex;
    gap: 0.75rem;
  }

  .control-btn {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    font-size: 1.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
  }

  .status-indicator {
    font-size: 2rem;
    animation: bounce 0.5s ease-in-out;
  }

  .status-indicator.won {
    animation: celebration 1s ease-in-out;
  }

  .status-indicator.lost {
    animation: shake 0.5s ease-in-out;
  }

  @keyframes bounce {
    0%,
    20%,
    60%,
    100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    80% {
      transform: translateY(-5px);
    }
  }

  @keyframes celebration {
    0%,
    100% {
      transform: scale(1) rotate(0deg);
    }
    25% {
      transform: scale(1.2) rotate(-5deg);
    }
    50% {
      transform: scale(1.1) rotate(5deg);
    }
    75% {
      transform: scale(1.2) rotate(-5deg);
    }
  }

  @keyframes shake {
    0%,
    100% {
      transform: translateX(0);
    }
    10%,
    30%,
    50%,
    70%,
    90% {
      transform: translateX(-5px);
    }
    20%,
    40%,
    60%,
    80% {
      transform: translateX(5px);
    }
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .header-content {
      flex-direction: column;
      gap: 1rem;
      padding: 1rem;
    }

    .logo-section {
      align-items: center;
      text-align: center;
    }

    .title {
      font-size: 1.25rem;
    }

    .stats-section {
      order: 3;
    }

    .controls-section {
      order: 2;
    }

    .control-btn {
      width: 44px;
      height: 44px;
      font-size: 1.125rem;
    }
  }

  @media (max-width: 480px) {
    .header-content {
      padding: 0.75rem;
    }

    .title {
      font-size: 1.125rem;
    }

    .subtitle {
      font-size: 0.75rem;
    }

    .stat-item {
      padding: 0.5rem 0.75rem;
      min-width: 70px;
    }

    .stat-icon {
      font-size: 1rem;
    }

    .stat-value {
      font-size: 1rem;
    }

    .control-btn {
      width: 40px;
      height: 40px;
      font-size: 1rem;
    }
  }
</style>
