<script>
  import { CELL_STATES, CELL_TYPES } from '../utils/constants.js';
  import { gameState, theme } from '../stores/gameStore.js';
  
  export let cell;
  export let onClick = () => {};
  export let onRightClick = () => {};
  
  $: isRevealed = cell.state === CELL_STATES.REVEALED;
  $: isFlagged = cell.state === CELL_STATES.FLAGGED;
  $: isMine = cell.type === CELL_TYPES.MINE;
  $: adjacentMines = cell.adjacentMines;
  
  function handleClick() {
    onClick(cell.row, cell.col);
  }
  
  function handleRightClick(event) {
    event.preventDefault();
    onRightClick(cell.row, cell.col);
  }
  
  function handleContextMenu(event) {
    event.preventDefault();
  }
</script>

<button
  class="cell"
  class:revealed={isRevealed}
  class:flagged={isFlagged}
  class:mine={isMine && isRevealed}
  class:number-{adjacentMines}={isRevealed && !isMine && adjacentMines > 0}
  class:dark={$theme === 'dark'}
  on:click={handleClick}
  on:contextmenu={handleRightClick}
  on:auxclick={handleContextMenu}
  disabled={isRevealed}
>
  {#if isFlagged}
    <span class="flag">🚩</span>
  {:else if isRevealed}
    {#if isMine}
      <span class="mine">💣</span>
    {:else if adjacentMines > 0}
      <span class="number">{adjacentMines}</span>
    {/if}
  {/if}
</button>

<style>
  .cell {
    width: 100%;
    height: 100%;
    min-width: 24px;
    min-height: 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.15s ease;
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 600;
    font-size: clamp(10px, 2.5vw, 14px);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
    
    /* Light theme neumorphic style */
    background: #f5f5f0;
    box-shadow: 
      6px 6px 12px rgba(0, 0, 0, 0.1),
      -6px -6px 12px rgba(255, 255, 255, 0.8);
  }
  
  .cell:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 
      8px 8px 16px rgba(0, 0, 0, 0.12),
      -8px -8px 16px rgba(255, 255, 255, 0.9);
  }
  
  .cell:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 
      inset 4px 4px 8px rgba(0, 0, 0, 0.1),
      inset -4px -4px 8px rgba(255, 255, 255, 0.8);
  }
  
  .cell.revealed {
    background: #e8e8e0;
    box-shadow: 
      inset 3px 3px 6px rgba(0, 0, 0, 0.1),
      inset -3px -3px 6px rgba(255, 255, 255, 0.6);
    cursor: default;
  }
  
  .cell.revealed:hover {
    transform: none;
    box-shadow: 
      inset 3px 3px 6px rgba(0, 0, 0, 0.1),
      inset -3px -3px 6px rgba(255, 255, 255, 0.6);
  }
  
  .cell.flagged {
    background: linear-gradient(135deg, #ff6b35, #ff8c42);
    box-shadow: 
      4px 4px 8px rgba(0, 0, 0, 0.15),
      -4px -4px 8px rgba(255, 255, 255, 0.7);
  }
  
  .cell.mine {
    background: linear-gradient(135deg, #ff4444, #ff6666);
    animation: explode 0.3s ease-out;
  }
  
  /* Dark theme */
  .cell.dark {
    background: #2d2d2d;
    box-shadow: 
      6px 6px 12px rgba(0, 0, 0, 0.3),
      -6px -6px 12px rgba(255, 255, 255, 0.1);
  }
  
  .cell.dark:hover:not(:disabled) {
    box-shadow: 
      8px 8px 16px rgba(0, 0, 0, 0.4),
      -8px -8px 16px rgba(255, 255, 255, 0.15);
  }
  
  .cell.dark:active:not(:disabled) {
    box-shadow: 
      inset 4px 4px 8px rgba(0, 0, 0, 0.3),
      inset -4px -4px 8px rgba(255, 255, 255, 0.1);
  }
  
  .cell.dark.revealed {
    background: #1a1a1a;
    box-shadow: 
      inset 3px 3px 6px rgba(0, 0, 0, 0.4),
      inset -3px -3px 6px rgba(255, 255, 255, 0.05);
  }
  
  .cell.dark.revealed:hover {
    box-shadow: 
      inset 3px 3px 6px rgba(0, 0, 0, 0.4),
      inset -3px -3px 6px rgba(255, 255, 255, 0.05);
  }
  
  .cell.dark.flagged {
    background: linear-gradient(135deg, #ff6b35, #ff8c42);
    box-shadow: 
      4px 4px 8px rgba(0, 0, 0, 0.4),
      -4px -4px 8px rgba(255, 255, 255, 0.1);
  }
  
  /* Number colors */
  .number {
    font-weight: 700;
  }
  
  .cell.number-1 .number { color: #2196f3; }
  .cell.number-2 .number { color: #4caf50; }
  .cell.number-3 .number { color: #ff6b35; }
  .cell.number-4 .number { color: #9c27b0; }
  .cell.number-5 .number { color: #795548; }
  .cell.number-6 .number { color: #607d8b; }
  .cell.number-7 .number { color: #000000; }
  .cell.number-8 .number { color: #424242; }
  
  /* Dark theme number colors */
  .cell.dark.number-1 .number { color: #64b5f6; }
  .cell.dark.number-2 .number { color: #81c784; }
  .cell.dark.number-3 .number { color: #ff6b35; }
  .cell.dark.number-4 .number { color: #ba68c8; }
  .cell.dark.number-5 .number { color: #a1887f; }
  .cell.dark.number-6 .number { color: #90a4ae; }
  .cell.dark.number-7 .number { color: #ffffff; }
  .cell.dark.number-8 .number { color: #bdbdbd; }
  
  .flag, .mine {
    font-size: clamp(8px, 2vw, 12px);
  }
  
  @keyframes explode {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
  }
  
  /* Mobile optimizations */
  @media (max-width: 480px) {
    .cell {
      min-width: 20px;
      min-height: 20px;
      border-radius: 6px;
      box-shadow: 
        4px 4px 8px rgba(0, 0, 0, 0.1),
        -4px -4px 8px rgba(255, 255, 255, 0.8);
    }
    
    .cell:hover:not(:disabled) {
      transform: none;
      box-shadow: 
        4px 4px 8px rgba(0, 0, 0, 0.1),
        -4px -4px 8px rgba(255, 255, 255, 0.8);
    }
    
    .cell.revealed {
      box-shadow: 
        inset 2px 2px 4px rgba(0, 0, 0, 0.1),
        inset -2px -2px 4px rgba(255, 255, 255, 0.6);
    }
    
    .cell.dark {
      box-shadow: 
        4px 4px 8px rgba(0, 0, 0, 0.3),
        -4px -4px 8px rgba(255, 255, 255, 0.1);
    }
    
    .cell.dark.revealed {
      box-shadow: 
        inset 2px 2px 4px rgba(0, 0, 0, 0.4),
        inset -2px -2px 4px rgba(255, 255, 255, 0.05);
    }
  }
</style>
