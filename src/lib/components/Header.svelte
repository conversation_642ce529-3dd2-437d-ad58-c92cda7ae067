<script>
  import { minesRemaining, gameTime, gameState, gameConfig, theme, settings } from '../stores/gameStore.js';
  import { GAME_STATES } from '../utils/constants.js';
  
  function formatTime(seconds) {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  
  function handleNewGame() {
    gameState.newGame($gameConfig);
  }
  
  function handleSettings() {
    settings.toggleSettings();
  }
  
  function handleThemeToggle() {
    theme.toggle();
  }
  
  $: gameStatus = $gameState.gameState;
  $: isPlaying = gameStatus === GAME_STATES.PLAYING;
  $: isWon = gameStatus === GAME_STATES.WON;
  $: isLost = gameStatus === GAME_STATES.LOST;
</script>

<header class="header" class:dark={$theme === 'dark'}>
  <div class="header-content">
    <!-- Logo and Title -->
    <div class="logo-section">
      <div class="logo">💣</div>
      <h1 class="title">Neumorsweeper</h1>
      <p class="subtitle">Neumorphic Minesweeper</p>
    </div>
    
    <!-- Game Controls -->
    <div class="controls">
      <button class="control-btn theme-btn" on:click={handleThemeToggle} title="Toggle theme">
        {$theme === 'dark' ? '☀️' : '🌙'}
      </button>
      <button class="control-btn settings-btn" on:click={handleSettings} title="Settings">
        ⚙️
      </button>
      <a 
        href="https://github.com/melvinchia3636/neumorsweeper" 
        target="_blank" 
        rel="noopener noreferrer"
        class="control-btn github-btn"
        title="View on GitHub"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
        </svg>
      </a>
    </div>
  </div>
  
  <!-- Game Stats -->
  <div class="stats">
    <div class="stat-item mines-counter">
      <div class="stat-icon">💣</div>
      <div class="stat-value">{$minesRemaining.toString().padStart(2, '0')}</div>
    </div>
    
    <button 
      class="new-game-btn"
      class:playing={isPlaying}
      class:won={isWon}
      class:lost={isLost}
      on:click={handleNewGame}
      title="New Game"
    >
      {#if isWon}
        😎
      {:else if isLost}
        😵
      {:else if isPlaying}
        😐
      {:else}
        🙂
      {/if}
    </button>
    
    <div class="stat-item timer">
      <div class="stat-icon">⏱️</div>
      <div class="stat-value">{formatTime($gameTime)}</div>
    </div>
  </div>
</header>

<style>
  .header {
    background: #f5f5f0;
    padding: 20px;
    border-radius: 20px;
    margin: 20px auto;
    max-width: 600px;
    box-shadow: 
      8px 8px 16px rgba(0, 0, 0, 0.1),
      -8px -8px 16px rgba(255, 255, 255, 0.8);
  }
  
  .header.dark {
    background: #2d2d2d;
    box-shadow: 
      8px 8px 16px rgba(0, 0, 0, 0.3),
      -8px -8px 16px rgba(255, 255, 255, 0.1);
  }
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .logo-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .logo {
    font-size: 24px;
    margin-bottom: 5px;
  }
  
  .title {
    font-size: 24px;
    font-weight: 700;
    margin: 0;
    color: #2c2c2c;
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  
  .subtitle {
    font-size: 12px;
    color: #666666;
    margin: 2px 0 0 0;
    font-weight: 400;
  }
  
  .header.dark .title {
    color: #ffffff;
  }
  
  .header.dark .subtitle {
    color: #cccccc;
  }
  
  .controls {
    display: flex;
    gap: 10px;
    align-items: center;
  }
  
  .control-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 12px;
    background: #f5f5f0;
    box-shadow: 
      4px 4px 8px rgba(0, 0, 0, 0.1),
      -4px -4px 8px rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.15s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    text-decoration: none;
    color: #2c2c2c;
  }
  
  .control-btn:hover {
    transform: translateY(-1px);
    box-shadow: 
      6px 6px 12px rgba(0, 0, 0, 0.12),
      -6px -6px 12px rgba(255, 255, 255, 0.9);
  }
  
  .control-btn:active {
    transform: translateY(0);
    box-shadow: 
      inset 2px 2px 4px rgba(0, 0, 0, 0.1),
      inset -2px -2px 4px rgba(255, 255, 255, 0.8);
  }
  
  .header.dark .control-btn {
    background: #2d2d2d;
    color: #ffffff;
    box-shadow: 
      4px 4px 8px rgba(0, 0, 0, 0.3),
      -4px -4px 8px rgba(255, 255, 255, 0.1);
  }
  
  .header.dark .control-btn:hover {
    box-shadow: 
      6px 6px 12px rgba(0, 0, 0, 0.4),
      -6px -6px 12px rgba(255, 255, 255, 0.15);
  }
  
  .header.dark .control-btn:active {
    box-shadow: 
      inset 2px 2px 4px rgba(0, 0, 0, 0.3),
      inset -2px -2px 4px rgba(255, 255, 255, 0.1);
  }
  
  .stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
  }
  
  .stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #e8e8e0;
    padding: 12px 16px;
    border-radius: 12px;
    box-shadow: 
      inset 3px 3px 6px rgba(0, 0, 0, 0.1),
      inset -3px -3px 6px rgba(255, 255, 255, 0.6);
    min-width: 80px;
    justify-content: center;
  }
  
  .header.dark .stat-item {
    background: #1a1a1a;
    box-shadow: 
      inset 3px 3px 6px rgba(0, 0, 0, 0.4),
      inset -3px -3px 6px rgba(255, 255, 255, 0.05);
  }
  
  .stat-icon {
    font-size: 16px;
  }
  
  .stat-value {
    font-size: 16px;
    font-weight: 600;
    color: #2c2c2c;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  }
  
  .header.dark .stat-value {
    color: #ffffff;
  }
  
  .new-game-btn {
    width: 60px;
    height: 60px;
    border: none;
    border-radius: 16px;
    background: #f5f5f0;
    box-shadow: 
      6px 6px 12px rgba(0, 0, 0, 0.1),
      -6px -6px 12px rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.15s ease;
    font-size: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .new-game-btn:hover {
    transform: translateY(-2px);
    box-shadow: 
      8px 8px 16px rgba(0, 0, 0, 0.12),
      -8px -8px 16px rgba(255, 255, 255, 0.9);
  }
  
  .new-game-btn:active {
    transform: translateY(0);
    box-shadow: 
      inset 3px 3px 6px rgba(0, 0, 0, 0.1),
      inset -3px -3px 6px rgba(255, 255, 255, 0.8);
  }
  
  .header.dark .new-game-btn {
    background: #2d2d2d;
    box-shadow: 
      6px 6px 12px rgba(0, 0, 0, 0.3),
      -6px -6px 12px rgba(255, 255, 255, 0.1);
  }
  
  .header.dark .new-game-btn:hover {
    box-shadow: 
      8px 8px 16px rgba(0, 0, 0, 0.4),
      -8px -8px 16px rgba(255, 255, 255, 0.15);
  }
  
  .header.dark .new-game-btn:active {
    box-shadow: 
      inset 3px 3px 6px rgba(0, 0, 0, 0.3),
      inset -3px -3px 6px rgba(255, 255, 255, 0.1);
  }
  
  /* Mobile responsive */
  @media (max-width: 768px) {
    .header {
      margin: 15px auto;
      padding: 15px;
      border-radius: 15px;
    }
    
    .header-content {
      margin-bottom: 15px;
    }
    
    .title {
      font-size: 20px;
    }
    
    .subtitle {
      font-size: 11px;
    }
    
    .control-btn {
      width: 36px;
      height: 36px;
      font-size: 14px;
    }
    
    .stats {
      gap: 15px;
    }
    
    .stat-item {
      padding: 10px 12px;
      min-width: 70px;
    }
    
    .stat-icon {
      font-size: 14px;
    }
    
    .stat-value {
      font-size: 14px;
    }
    
    .new-game-btn {
      width: 50px;
      height: 50px;
      font-size: 20px;
    }
  }
  
  @media (max-width: 480px) {
    .header {
      margin: 10px;
      padding: 12px;
    }
    
    .logo-section {
      align-items: center;
    }
    
    .title {
      font-size: 18px;
    }
    
    .subtitle {
      font-size: 10px;
    }
    
    .controls {
      gap: 8px;
    }
    
    .control-btn {
      width: 32px;
      height: 32px;
      font-size: 12px;
    }
    
    .stats {
      gap: 10px;
    }
    
    .stat-item {
      padding: 8px 10px;
      min-width: 60px;
    }
    
    .new-game-btn {
      width: 45px;
      height: 45px;
      font-size: 18px;
    }
  }
</style>
