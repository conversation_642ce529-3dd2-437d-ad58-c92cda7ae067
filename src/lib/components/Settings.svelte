<script>
  import { settings, gameConfig, gameState, theme } from '../stores/gameStore.js';
  import { DIFFICULTIES } from '../utils/constants.js';
  
  function handleDifficultyChange(difficulty) {
    gameConfig.setDifficulty(difficulty);
    gameState.newGame($gameConfig);
    settings.closeSettings();
  }
  
  function handleClose() {
    settings.closeSettings();
  }
  
  function handleOverlayClick(event) {
    if (event.target === event.currentTarget) {
      handleClose();
    }
  }
  
  $: isOpen = $settings.showSettings;
  $: currentDifficulty = $gameConfig.difficulty;
</script>

{#if isOpen}
  <div class="settings-overlay" on:click={handleOverlayClick}>
    <div class="settings-modal" class:dark={$theme === 'dark'}>
      <div class="settings-header">
        <div class="settings-title">
          <div class="settings-icon">⚙️</div>
          <div>
            <h2>Settings</h2>
            <p>Customize your game</p>
          </div>
        </div>
        <button class="close-btn" on:click={handleClose}>✕</button>
      </div>
      
      <div class="settings-content">
        <div class="setting-section">
          <h3>Dark theme</h3>
          <p>Enable dark theme for the game</p>
          <label class="toggle-switch">
            <input 
              type="checkbox" 
              checked={$theme === 'dark'}
              on:change={theme.toggle}
            />
            <span class="slider"></span>
          </label>
        </div>
        
        <div class="setting-section">
          <h3>Difficulty</h3>
          <p>Customize the board size and mine count</p>
          <div class="difficulty-options">
            {#each Object.entries(DIFFICULTIES) as [key, difficulty]}
              <button 
                class="difficulty-btn"
                class:active={currentDifficulty === key}
                on:click={() => handleDifficultyChange(key)}
              >
                <div class="difficulty-name">{difficulty.name}</div>
                <div class="difficulty-desc">{difficulty.description}</div>
              </button>
            {/each}
          </div>
        </div>
      </div>
      
      <div class="settings-footer">
        <p>Made with ❤️ by <a href="https://github.com/melvinchia3636" target="_blank" rel="noopener noreferrer">Melvin Chia</a>. Project under MIT License.</p>
      </div>
    </div>
  </div>
{/if}

<style>
  .settings-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
    backdrop-filter: blur(4px);
  }
  
  .settings-modal {
    background: #f5f5f0;
    border-radius: 20px;
    box-shadow: 
      12px 12px 24px rgba(0, 0, 0, 0.15),
      -12px -12px 24px rgba(255, 255, 255, 0.9);
    max-width: 500px;
    width: 100%;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  
  .settings-modal.dark {
    background: #2d2d2d;
    box-shadow: 
      12px 12px 24px rgba(0, 0, 0, 0.4),
      -12px -12px 24px rgba(255, 255, 255, 0.1);
  }
  
  .settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  .settings-modal.dark .settings-header {
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
  
  .settings-title {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .settings-icon {
    font-size: 24px;
  }
  
  .settings-title h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 700;
    color: #2c2c2c;
  }
  
  .settings-title p {
    margin: 2px 0 0 0;
    font-size: 12px;
    color: #666666;
  }
  
  .settings-modal.dark .settings-title h2 {
    color: #ffffff;
  }
  
  .settings-modal.dark .settings-title p {
    color: #cccccc;
  }
  
  .close-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 8px;
    background: #e8e8e0;
    box-shadow: 
      2px 2px 4px rgba(0, 0, 0, 0.1),
      -2px -2px 4px rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.15s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #666666;
  }
  
  .close-btn:hover {
    transform: translateY(-1px);
    box-shadow: 
      3px 3px 6px rgba(0, 0, 0, 0.12),
      -3px -3px 6px rgba(255, 255, 255, 0.9);
  }
  
  .close-btn:active {
    transform: translateY(0);
    box-shadow: 
      inset 1px 1px 2px rgba(0, 0, 0, 0.1),
      inset -1px -1px 2px rgba(255, 255, 255, 0.8);
  }
  
  .settings-modal.dark .close-btn {
    background: #1a1a1a;
    color: #cccccc;
    box-shadow: 
      2px 2px 4px rgba(0, 0, 0, 0.3),
      -2px -2px 4px rgba(255, 255, 255, 0.1);
  }
  
  .settings-modal.dark .close-btn:hover {
    box-shadow: 
      3px 3px 6px rgba(0, 0, 0, 0.4),
      -3px -3px 6px rgba(255, 255, 255, 0.15);
  }
  
  .settings-modal.dark .close-btn:active {
    box-shadow: 
      inset 1px 1px 2px rgba(0, 0, 0, 0.3),
      inset -1px -1px 2px rgba(255, 255, 255, 0.1);
  }
  
  .settings-content {
    padding: 24px;
    overflow-y: auto;
    flex: 1;
  }
  
  .setting-section {
    margin-bottom: 32px;
  }
  
  .setting-section:last-child {
    margin-bottom: 0;
  }
  
  .setting-section h3 {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
    color: #2c2c2c;
  }
  
  .setting-section p {
    margin: 0 0 16px 0;
    font-size: 12px;
    color: #666666;
  }
  
  .settings-modal.dark .setting-section h3 {
    color: #ffffff;
  }
  
  .settings-modal.dark .setting-section p {
    color: #cccccc;
  }
  
  .toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
  }
  
  .toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #e8e8e0;
    border-radius: 12px;
    transition: all 0.2s ease;
    box-shadow: 
      inset 2px 2px 4px rgba(0, 0, 0, 0.1),
      inset -2px -2px 4px rgba(255, 255, 255, 0.8);
  }
  
  .slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background: #f5f5f0;
    border-radius: 50%;
    transition: all 0.2s ease;
    box-shadow: 
      2px 2px 4px rgba(0, 0, 0, 0.1),
      -2px -2px 4px rgba(255, 255, 255, 0.8);
  }
  
  input:checked + .slider {
    background: #ff6b35;
  }
  
  input:checked + .slider:before {
    transform: translateX(26px);
  }
  
  .settings-modal.dark .slider {
    background: #1a1a1a;
    box-shadow: 
      inset 2px 2px 4px rgba(0, 0, 0, 0.3),
      inset -2px -2px 4px rgba(255, 255, 255, 0.1);
  }
  
  .settings-modal.dark .slider:before {
    background: #2d2d2d;
    box-shadow: 
      2px 2px 4px rgba(0, 0, 0, 0.3),
      -2px -2px 4px rgba(255, 255, 255, 0.1);
  }
  
  .difficulty-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .difficulty-btn {
    background: #e8e8e0;
    border: none;
    border-radius: 12px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.15s ease;
    text-align: left;
    box-shadow: 
      4px 4px 8px rgba(0, 0, 0, 0.1),
      -4px -4px 8px rgba(255, 255, 255, 0.8);
  }
  
  .difficulty-btn:hover {
    transform: translateY(-1px);
    box-shadow: 
      6px 6px 12px rgba(0, 0, 0, 0.12),
      -6px -6px 12px rgba(255, 255, 255, 0.9);
  }
  
  .difficulty-btn.active {
    background: linear-gradient(135deg, #ff6b35, #ff8c42);
    color: white;
    box-shadow: 
      4px 4px 8px rgba(0, 0, 0, 0.15),
      -4px -4px 8px rgba(255, 255, 255, 0.7);
  }
  
  .difficulty-btn.active:hover {
    box-shadow: 
      6px 6px 12px rgba(0, 0, 0, 0.2),
      -6px -6px 12px rgba(255, 255, 255, 0.8);
  }
  
  .settings-modal.dark .difficulty-btn {
    background: #1a1a1a;
    color: #ffffff;
    box-shadow: 
      4px 4px 8px rgba(0, 0, 0, 0.3),
      -4px -4px 8px rgba(255, 255, 255, 0.1);
  }
  
  .settings-modal.dark .difficulty-btn:hover {
    box-shadow: 
      6px 6px 12px rgba(0, 0, 0, 0.4),
      -6px -6px 12px rgba(255, 255, 255, 0.15);
  }
  
  .difficulty-name {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 4px;
  }
  
  .difficulty-desc {
    font-size: 12px;
    opacity: 0.8;
  }
  
  .settings-footer {
    padding: 16px 24px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    text-align: center;
  }
  
  .settings-modal.dark .settings-footer {
    border-top-color: rgba(255, 255, 255, 0.1);
  }
  
  .settings-footer p {
    margin: 0;
    font-size: 11px;
    color: #666666;
  }
  
  .settings-footer a {
    color: #ff6b35;
    text-decoration: none;
  }
  
  .settings-footer a:hover {
    text-decoration: underline;
  }
  
  .settings-modal.dark .settings-footer p {
    color: #cccccc;
  }
  
  /* Mobile responsive */
  @media (max-width: 768px) {
    .settings-overlay {
      padding: 10px;
    }
    
    .settings-modal {
      border-radius: 15px;
      max-height: 90vh;
    }
    
    .settings-header {
      padding: 20px;
    }
    
    .settings-content {
      padding: 20px;
    }
    
    .setting-section {
      margin-bottom: 24px;
    }
    
    .settings-footer {
      padding: 12px 20px;
    }
  }
</style>
