<script>
  import { gameState, gameConfig, theme } from "../stores/gameStore.js";
  import Cell from "./Cell.svelte";

  $: board = $gameState.board;
  $: config = $gameConfig;

  function handleCellClick(row, col) {
    gameState.cellClick(row, col);
  }

  function handleCellRightClick(row, col) {
    gameState.cellRightClick(row, col);
  }

  // Calculate grid size for responsive design
  $: gridCols = config.width;
  $: gridRows = config.height;
  $: cellSize =
    typeof window !== "undefined"
      ? Math.min(
          Math.floor((window.innerWidth - 40) / gridCols),
          Math.floor((window.innerHeight - 200) / gridRows),
          40
        )
      : 32;
</script>

<div
  class="game-board"
  class:dark={$theme === "dark"}
  style="
    --grid-cols: {gridCols};
    --grid-rows: {gridRows};
    --cell-size: {cellSize}px;
  "
>
  {#each board as row, rowIndex}
    {#each row as cell, colIndex}
      <Cell
        {cell}
        onClick={handleCellClick}
        onRightClick={handleCellRightClick}
      />
    {/each}
  {/each}
</div>

<style>
  .game-board {
    display: grid;
    grid-template-columns: repeat(var(--grid-cols), var(--cell-size));
    grid-template-rows: repeat(var(--grid-rows), var(--cell-size));
    gap: 4px;
    padding: 20px;
    border-radius: 20px;
    background: #f5f5f0;
    box-shadow:
      inset 8px 8px 16px rgba(0, 0, 0, 0.1),
      inset -8px -8px 16px rgba(255, 255, 255, 0.8);
    margin: 20px auto;
    width: fit-content;
    max-width: calc(100vw - 40px);
    overflow: auto;
  }

  .game-board.dark {
    background: #1a1a1a;
    box-shadow:
      inset 8px 8px 16px rgba(0, 0, 0, 0.4),
      inset -8px -8px 16px rgba(255, 255, 255, 0.05);
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .game-board {
      padding: 15px;
      gap: 3px;
      border-radius: 15px;
      margin: 15px auto;
      box-shadow:
        inset 6px 6px 12px rgba(0, 0, 0, 0.1),
        inset -6px -6px 12px rgba(255, 255, 255, 0.8);
    }

    .game-board.dark {
      box-shadow:
        inset 6px 6px 12px rgba(0, 0, 0, 0.4),
        inset -6px -6px 12px rgba(255, 255, 255, 0.05);
    }
  }

  @media (max-width: 480px) {
    .game-board {
      padding: 10px;
      gap: 2px;
      border-radius: 12px;
      margin: 10px auto;
      box-shadow:
        inset 4px 4px 8px rgba(0, 0, 0, 0.1),
        inset -4px -4px 8px rgba(255, 255, 255, 0.8);
    }

    .game-board.dark {
      box-shadow:
        inset 4px 4px 8px rgba(0, 0, 0, 0.4),
        inset -4px -4px 8px rgba(255, 255, 255, 0.05);
    }
  }

  /* Handle very wide boards */
  @media (max-width: 1200px) {
    .game-board {
      transform-origin: center;
      transform: scale(
        min(1, calc(100vw / (var(--grid-cols) * var(--cell-size) + 80px)))
      );
    }
  }
</style>
