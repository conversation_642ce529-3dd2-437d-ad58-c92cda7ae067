<script>
  import { createEventDispatcher } from "svelte";
  import Cell from "./Cell.svelte";

  export let board = [];
  export let gameState = "ready";

  const dispatch = createEventDispatcher();

  function handleCellReveal(event) {
    dispatch("cell-reveal", event.detail);
  }

  function handleCellFlag(event) {
    dispatch("cell-flag", event.detail);
  }

  function getBoardStyle() {
    if (board.length === 0) return "";

    const width = board[0].length;
    const height = board.length;

    return `
      grid-template-columns: repeat(${width}, 1fr);
      grid-template-rows: repeat(${height}, 1fr);
      gap: 2px;
    `;
  }
</script>

<div class="game-container">
  {#if board.length > 0}
    <div class="game-board neu-inset" style={getBoardStyle()}>
      {#each board as row}
        {#each row as cell}
          <Cell
            {cell}
            {gameState}
            on:reveal={handleCellReveal}
            on:flag={handleCellFlag}
          />
        {/each}
      {/each}
    </div>
  {:else}
    <div class="empty-board neu-inset">
      <div class="empty-content">
        <div class="empty-icon">🎮</div>
        <h3>Ready to Play!</h3>
        <p>Click "New Game" to start</p>
      </div>
    </div>
  {/if}
</div>

<style>
  .game-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
  }

  .game-board {
    display: grid;
    padding: 1rem;
    background: var(--bg-primary);
    border-radius: 16px;
    max-width: calc(100vw - 2rem);
    max-height: calc(100vh - 300px);
    overflow: auto;
    aspect-ratio: auto;
  }

  .empty-board {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 300px;
    height: 200px;
    background: var(--bg-primary);
    border-radius: 16px;
  }

  .empty-content {
    text-align: center;
    color: var(--text-secondary);
  }

  .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }

  .empty-content h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
    color: var(--text-primary);
  }

  .empty-content p {
    margin: 0;
    font-size: 0.875rem;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .game-board {
      padding: 0.75rem;
      border-radius: 12px;
      max-height: 60vh;
    }

    .empty-board {
      width: 250px;
      height: 150px;
    }

    .empty-icon {
      font-size: 2.5rem;
    }

    .empty-content h3 {
      font-size: 1.125rem;
    }
  }

  @media (max-width: 480px) {
    .game-board {
      padding: 0.5rem;
      border-radius: 10px;
      max-height: 50vh;
    }

    .empty-board {
      width: 200px;
      height: 120px;
    }

    .empty-icon {
      font-size: 2rem;
    }

    .empty-content h3 {
      font-size: 1rem;
    }

    .empty-content p {
      font-size: 0.75rem;
    }
  }

  /* Custom scrollbar for game board */
  .game-board::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .game-board::-webkit-scrollbar-track {
    background: var(--bg-primary);
    border-radius: 4px;
  }

  .game-board::-webkit-scrollbar-thumb {
    background: var(--shadow-dark);
    border-radius: 4px;
  }

  .game-board::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
  }
</style>
