import { difficulties } from './stores.js';

// Create an empty board
export function createBoard(width, height) {
  const board = [];
  for (let row = 0; row < height; row++) {
    board[row] = [];
    for (let col = 0; col < width; col++) {
      board[row][col] = {
        isMine: false,
        isRevealed: false,
        isFlagged: false,
        neighborMines: 0,
        row,
        col
      };
    }
  }
  return board;
}

// Place mines randomly on the board, avoiding the first clicked cell
export function placeMines(board, mineCount, firstClickRow, firstClickCol) {
  const height = board.length;
  const width = board[0].length;
  let minesPlaced = 0;

  while (minesPlaced < mineCount) {
    const row = Math.floor(Math.random() * height);
    const col = Math.floor(Math.random() * width);

    // Don't place mine on first click or if already has mine
    if ((row === firstClickRow && col === firstClickCol) || board[row][col].isMine) {
      continue;
    }

    board[row][col].isMine = true;
    minesPlaced++;
  }

  // Calculate neighbor mine counts
  calculateNeighborMines(board);
  return board;
}

// Calculate the number of neighboring mines for each cell
export function calculateNeighborMines(board) {
  const height = board.length;
  const width = board[0].length;

  for (let row = 0; row < height; row++) {
    for (let col = 0; col < width; col++) {
      if (!board[row][col].isMine) {
        board[row][col].neighborMines = countNeighborMines(board, row, col);
      }
    }
  }
}

// Count mines in neighboring cells
export function countNeighborMines(board, row, col) {
  const height = board.length;
  const width = board[0].length;
  let count = 0;

  for (let r = Math.max(0, row - 1); r <= Math.min(height - 1, row + 1); r++) {
    for (let c = Math.max(0, col - 1); c <= Math.min(width - 1, col + 1); c++) {
      if (r !== row || c !== col) {
        if (board[r][c].isMine) {
          count++;
        }
      }
    }
  }

  return count;
}

// Reveal a cell and potentially cascade to neighbors
export function revealCell(board, row, col) {
  const height = board.length;
  const width = board[0].length;
  
  if (row < 0 || row >= height || col < 0 || col >= width) {
    return board;
  }

  const cell = board[row][col];
  
  if (cell.isRevealed || cell.isFlagged) {
    return board;
  }

  // Create a new board to maintain immutability
  const newBoard = board.map(row => row.map(cell => ({ ...cell })));
  newBoard[row][col].isRevealed = true;

  // If the cell has no neighboring mines, reveal all neighbors
  if (newBoard[row][col].neighborMines === 0 && !newBoard[row][col].isMine) {
    for (let r = Math.max(0, row - 1); r <= Math.min(height - 1, row + 1); r++) {
      for (let c = Math.max(0, col - 1); c <= Math.min(width - 1, col + 1); c++) {
        if (r !== row || c !== col) {
          if (!newBoard[r][c].isRevealed && !newBoard[r][c].isFlagged) {
            return revealCell(newBoard, r, c);
          }
        }
      }
    }
  }

  return newBoard;
}

// Toggle flag on a cell
export function toggleFlag(board, row, col) {
  const height = board.length;
  const width = board[0].length;
  
  if (row < 0 || row >= height || col < 0 || col >= width) {
    return board;
  }

  const cell = board[row][col];
  
  if (cell.isRevealed) {
    return board;
  }

  // Create a new board to maintain immutability
  const newBoard = board.map(row => row.map(cell => ({ ...cell })));
  newBoard[row][col].isFlagged = !newBoard[row][col].isFlagged;

  return newBoard;
}

// Check if the game is won
export function checkWinCondition(board) {
  const height = board.length;
  const width = board[0].length;

  for (let row = 0; row < height; row++) {
    for (let col = 0; col < width; col++) {
      const cell = board[row][col];
      // If there's a non-mine cell that's not revealed, game is not won
      if (!cell.isMine && !cell.isRevealed) {
        return false;
      }
    }
  }

  return true;
}

// Count revealed cells
export function countRevealedCells(board) {
  let count = 0;
  for (let row = 0; row < board.length; row++) {
    for (let col = 0; col < board[0].length; col++) {
      if (board[row][col].isRevealed) {
        count++;
      }
    }
  }
  return count;
}

// Count flagged cells
export function countFlaggedCells(board) {
  let count = 0;
  for (let row = 0; row < board.length; row++) {
    for (let col = 0; col < board[0].length; col++) {
      if (board[row][col].isFlagged) {
        count++;
      }
    }
  }
  return count;
}

// Initialize a new game
export function initializeGame(difficulty) {
  const config = difficulties[difficulty];
  const board = createBoard(config.width, config.height);
  
  return {
    board,
    mineCount: config.mines,
    width: config.width,
    height: config.height
  };
}
