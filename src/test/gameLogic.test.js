import { describe, it, expect, beforeEach } from 'vitest';
import {
  createBoard,
  placeMines,
  countAdjacentMines,
  revealCell,
  toggleFlag,
  checkWin,
  revealAllMines,
  countFlags
} from '../lib/utils/gameLogic.js';
import { CELL_STATES, CELL_TYPES } from '../lib/utils/constants.js';

describe('Game Logic', () => {
  describe('createBoard', () => {
    it('should create a board with correct dimensions', () => {
      const board = createBoard(10, 8);
      expect(board).toHaveLength(8);
      expect(board[0]).toHaveLength(10);
    });

    it('should initialize all cells as empty and hidden', () => {
      const board = createBoard(3, 3);
      for (let row = 0; row < 3; row++) {
        for (let col = 0; col < 3; col++) {
          const cell = board[row][col];
          expect(cell.type).toBe(CELL_TYPES.EMPTY);
          expect(cell.state).toBe(CELL_STATES.HIDDEN);
          expect(cell.adjacentMines).toBe(0);
          expect(cell.row).toBe(row);
          expect(cell.col).toBe(col);
        }
      }
    });
  });

  describe('placeMines', () => {
    let board;

    beforeEach(() => {
      board = createBoard(5, 5);
    });

    it('should place the correct number of mines', () => {
      const mineCount = 5;
      const updatedBoard = placeMines(board, mineCount, 0, 0);

      let actualMines = 0;
      for (let row = 0; row < 5; row++) {
        for (let col = 0; col < 5; col++) {
          if (updatedBoard[row][col].type === CELL_TYPES.MINE) {
            actualMines++;
          }
        }
      }

      expect(actualMines).toBe(mineCount);
    });

    it('should not place mines around the first click', () => {
      const firstClickRow = 2;
      const firstClickCol = 2;
      const updatedBoard = placeMines(board, 10, firstClickRow, firstClickCol);

      // Check 3x3 area around first click
      for (let dr = -1; dr <= 1; dr++) {
        for (let dc = -1; dc <= 1; dc++) {
          const row = firstClickRow + dr;
          const col = firstClickCol + dc;
          if (row >= 0 && row < 5 && col >= 0 && col < 5) {
            expect(updatedBoard[row][col].type).toBe(CELL_TYPES.EMPTY);
          }
        }
      }
    });

    it('should calculate adjacent mine counts correctly', () => {
      // Create a small board and manually place mines
      const smallBoard = createBoard(3, 3);
      smallBoard[0][0].type = CELL_TYPES.MINE;
      smallBoard[0][2].type = CELL_TYPES.MINE;

      // Recalculate adjacent mines
      for (let row = 0; row < 3; row++) {
        for (let col = 0; col < 3; col++) {
          if (smallBoard[row][col].type !== CELL_TYPES.MINE) {
            smallBoard[row][col].adjacentMines = countAdjacentMines(smallBoard, row, col);
          }
        }
      }

      expect(smallBoard[0][1].adjacentMines).toBe(2); // Between two mines
      expect(smallBoard[1][0].adjacentMines).toBe(1); // Adjacent to one mine
      expect(smallBoard[1][1].adjacentMines).toBe(2); // Adjacent to two mines
      expect(smallBoard[2][2].adjacentMines).toBe(0); // Not adjacent to any mine (corrected)
    });
  });

  describe('countAdjacentMines', () => {
    it('should count adjacent mines correctly', () => {
      const board = createBoard(3, 3);
      board[0][0].type = CELL_TYPES.MINE;
      board[0][2].type = CELL_TYPES.MINE;
      board[2][1].type = CELL_TYPES.MINE;

      expect(countAdjacentMines(board, 1, 1)).toBe(3); // Center cell
      expect(countAdjacentMines(board, 0, 1)).toBe(2); // Top center
      expect(countAdjacentMines(board, 2, 0)).toBe(1); // Bottom left
      expect(countAdjacentMines(board, 2, 2)).toBe(1); // Bottom right
    });

    it('should handle edge cases', () => {
      const board = createBoard(3, 3);
      board[1][1].type = CELL_TYPES.MINE;

      expect(countAdjacentMines(board, 0, 0)).toBe(1); // Corner
      expect(countAdjacentMines(board, 0, 1)).toBe(1); // Edge
      expect(countAdjacentMines(board, 2, 2)).toBe(1); // Opposite corner
    });
  });

  describe('revealCell', () => {
    let board;

    beforeEach(() => {
      board = createBoard(5, 5);
    });

    it('should reveal a single cell', () => {
      // Place a mine to prevent cascade revealing
      board[1][1].type = CELL_TYPES.MINE;
      board[2][2].adjacentMines = 1; // Update adjacent count

      const result = revealCell(board, 2, 2);
      expect(result.board[2][2].state).toBe(CELL_STATES.REVEALED);
      expect(result.revealedCount).toBe(1);
    });

    it('should not reveal already revealed cells', () => {
      board[2][2].state = CELL_STATES.REVEALED;
      const result = revealCell(board, 2, 2);
      expect(result.revealedCount).toBe(0);
    });

    it('should not reveal flagged cells', () => {
      board[2][2].state = CELL_STATES.FLAGGED;
      const result = revealCell(board, 2, 2);
      expect(result.board[2][2].state).toBe(CELL_STATES.FLAGGED);
      expect(result.revealedCount).toBe(0);
    });

    it('should cascade reveal empty cells', () => {
      // Create a board with no mines - should reveal all cells
      const result = revealCell(board, 2, 2);
      expect(result.revealedCount).toBe(25); // All cells should be revealed
    });

    it('should handle out of bounds gracefully', () => {
      const result = revealCell(board, -1, -1);
      expect(result.revealedCount).toBe(0);

      const result2 = revealCell(board, 10, 10);
      expect(result2.revealedCount).toBe(0);
    });
  });

  describe('toggleFlag', () => {
    let board;

    beforeEach(() => {
      board = createBoard(3, 3);
    });

    it('should flag a hidden cell', () => {
      toggleFlag(board, 1, 1);
      expect(board[1][1].state).toBe(CELL_STATES.FLAGGED);
    });

    it('should unflag a flagged cell', () => {
      board[1][1].state = CELL_STATES.FLAGGED;
      toggleFlag(board, 1, 1);
      expect(board[1][1].state).toBe(CELL_STATES.HIDDEN);
    });

    it('should not flag revealed cells', () => {
      board[1][1].state = CELL_STATES.REVEALED;
      toggleFlag(board, 1, 1);
      expect(board[1][1].state).toBe(CELL_STATES.REVEALED);
    });
  });

  describe('checkWin', () => {
    let board;

    beforeEach(() => {
      board = createBoard(3, 3);
    });

    it('should return true when all non-mine cells are revealed', () => {
      // Place one mine
      board[0][0].type = CELL_TYPES.MINE;

      // Reveal all other cells
      for (let row = 0; row < 3; row++) {
        for (let col = 0; col < 3; col++) {
          if (board[row][col].type !== CELL_TYPES.MINE) {
            board[row][col].state = CELL_STATES.REVEALED;
          }
        }
      }

      expect(checkWin(board, 1)).toBe(true);
    });

    it('should return false when some non-mine cells are not revealed', () => {
      board[0][0].type = CELL_TYPES.MINE;
      board[1][1].state = CELL_STATES.REVEALED;

      expect(checkWin(board, 1)).toBe(false);
    });
  });

  describe('revealAllMines', () => {
    let board;

    beforeEach(() => {
      board = createBoard(3, 3);
      board[0][0].type = CELL_TYPES.MINE;
      board[1][1].type = CELL_TYPES.MINE;
      board[2][2].type = CELL_TYPES.MINE;
      board[2][2].state = CELL_STATES.FLAGGED; // This one should stay flagged
    });

    it('should reveal all unflagged mines', () => {
      revealAllMines(board);

      expect(board[0][0].state).toBe(CELL_STATES.REVEALED);
      expect(board[1][1].state).toBe(CELL_STATES.REVEALED);
      expect(board[2][2].state).toBe(CELL_STATES.FLAGGED); // Should remain flagged
    });
  });

  describe('countFlags', () => {
    let board;

    beforeEach(() => {
      board = createBoard(3, 3);
    });

    it('should count flagged cells correctly', () => {
      board[0][0].state = CELL_STATES.FLAGGED;
      board[1][1].state = CELL_STATES.FLAGGED;
      board[2][2].state = CELL_STATES.REVEALED;

      expect(countFlags(board)).toBe(2);
    });

    it('should return 0 when no cells are flagged', () => {
      expect(countFlags(board)).toBe(0);
    });
  });
});
