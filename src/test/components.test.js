import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, fireEvent, screen } from '@testing-library/svelte';

import Cell from '../lib/components/Cell.svelte';
import Header from '../lib/components/Header.svelte';
import Settings from '../lib/components/Settings.svelte';
import { CELL_STATES, CELL_TYPES } from '../lib/utils/constants.js';

// Mock stores
vi.mock('../lib/stores/gameStore.js', () => ({
  theme: { subscribe: vi.fn(() => vi.fn()) },
  gameState: {
    subscribe: vi.fn(() => vi.fn()),
    newGame: vi.fn(),
    cellClick: vi.fn(),
    cellRightClick: vi.fn()
  },
  gameConfig: { subscribe: vi.fn(() => vi.fn()) },
  minesRemaining: { subscribe: vi.fn(() => vi.fn()) },
  gameTime: { subscribe: vi.fn(() => vi.fn()) },
  settings: {
    subscribe: vi.fn(() => vi.fn()),
    toggleSettings: vi.fn(),
    closeSettings: vi.fn()
  }
}));

describe('Cell Component', () => {
  const mockCell = {
    row: 1,
    col: 1,
    type: CELL_TYPES.EMPTY,
    state: CELL_STATES.HIDDEN,
    adjacentMines: 0
  };

  it('should render a hidden cell', () => {
    const { container } = render(Cell, {
      props: {
        cell: mockCell,
        onClick: vi.fn(),
        onRightClick: vi.fn()
      }
    });

    const button = container.querySelector('button');
    expect(button).toBeInTheDocument();
    expect(button).not.toHaveClass('revealed');
    expect(button).not.toHaveClass('flagged');
  });

  it('should render a flagged cell', () => {
    const flaggedCell = { ...mockCell, state: CELL_STATES.FLAGGED };
    const { container } = render(Cell, {
      props: {
        cell: flaggedCell,
        onClick: vi.fn(),
        onRightClick: vi.fn()
      }
    });

    const button = container.querySelector('button');
    expect(button).toHaveClass('flagged');
    expect(screen.getByText('🚩')).toBeInTheDocument();
  });

  it('should render a revealed cell with number', () => {
    const revealedCell = {
      ...mockCell,
      state: CELL_STATES.REVEALED,
      adjacentMines: 3
    };
    const { container } = render(Cell, {
      props: {
        cell: revealedCell,
        onClick: vi.fn(),
        onRightClick: vi.fn()
      }
    });

    const button = container.querySelector('button');
    expect(button).toHaveClass('revealed');
    expect(button).toHaveClass('number-3');
    expect(screen.getByText('3')).toBeInTheDocument();
  });

  it('should render a revealed mine', () => {
    const mineCell = {
      ...mockCell,
      type: CELL_TYPES.MINE,
      state: CELL_STATES.REVEALED
    };
    const { container } = render(Cell, {
      props: {
        cell: mineCell,
        onClick: vi.fn(),
        onRightClick: vi.fn()
      }
    });

    const button = container.querySelector('button');
    expect(button).toHaveClass('mine');
    expect(screen.getByText('💣')).toBeInTheDocument();
  });

  it('should call onClick when clicked', async () => {
    const onClick = vi.fn();
    const { container } = render(Cell, {
      props: {
        cell: mockCell,
        onClick,
        onRightClick: vi.fn()
      }
    });

    const button = container.querySelector('button');
    await fireEvent.click(button);

    expect(onClick).toHaveBeenCalledWith(1, 1);
  });

  it('should call onRightClick when right-clicked', async () => {
    const onRightClick = vi.fn();
    const { container } = render(Cell, {
      props: {
        cell: mockCell,
        onClick: vi.fn(),
        onRightClick
      }
    });

    const button = container.querySelector('button');
    await fireEvent.contextMenu(button);

    expect(onRightClick).toHaveBeenCalledWith(1, 1);
  });

  it('should be disabled when revealed', () => {
    const revealedCell = { ...mockCell, state: CELL_STATES.REVEALED };
    const { container } = render(Cell, {
      props: {
        cell: revealedCell,
        onClick: vi.fn(),
        onRightClick: vi.fn()
      }
    });

    const button = container.querySelector('button');
    expect(button).toBeDisabled();
  });
});

describe('Header Component', () => {
  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();
  });

  it('should render the game title', () => {
    render(Header);
    expect(screen.getByText('Neumorsweeper')).toBeInTheDocument();
    expect(screen.getByText('Neumorphic Minesweeper')).toBeInTheDocument();
  });

  it('should render control buttons', () => {
    render(Header);

    // Theme toggle button
    const themeBtn = screen.getByTitle('Toggle theme');
    expect(themeBtn).toBeInTheDocument();

    // Settings button
    const settingsBtn = screen.getByTitle('Settings');
    expect(settingsBtn).toBeInTheDocument();

    // GitHub link
    const githubLink = screen.getByTitle('View on GitHub');
    expect(githubLink).toBeInTheDocument();
    expect(githubLink).toHaveAttribute('href', 'https://github.com/melvinchia3636/neumorsweeper');
  });

  it('should render game stats', () => {
    render(Header);

    // Mine counter
    expect(screen.getByText('💣')).toBeInTheDocument();

    // Timer
    expect(screen.getByText('⏱️')).toBeInTheDocument();

    // New game button
    const newGameBtn = screen.getByTitle('New Game');
    expect(newGameBtn).toBeInTheDocument();
  });
});

describe('Settings Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should not render when closed', () => {
    // Mock settings store to return closed state
    const mockSettings = { showSettings: false };
    vi.doMock('../lib/stores/gameStore.js', () => ({
      settings: {
        subscribe: vi.fn((callback) => {
          callback(mockSettings);
          return vi.fn();
        })
      }
    }));

    const { container } = render(Settings);
    expect(container.querySelector('.settings-overlay')).not.toBeInTheDocument();
  });

  it('should render when open', () => {
    // Mock settings store to return open state
    const mockSettings = { showSettings: true };
    const mockTheme = 'light';
    const mockGameConfig = { difficulty: 'EASY' };

    vi.doMock('../lib/stores/gameStore.js', () => ({
      settings: {
        subscribe: vi.fn((callback) => {
          callback(mockSettings);
          return vi.fn();
        }),
        closeSettings: vi.fn()
      },
      theme: {
        subscribe: vi.fn((callback) => {
          callback(mockTheme);
          return vi.fn();
        }),
        toggle: vi.fn()
      },
      gameConfig: {
        subscribe: vi.fn((callback) => {
          callback(mockGameConfig);
          return vi.fn();
        }),
        setDifficulty: vi.fn()
      },
      gameState: {
        newGame: vi.fn()
      }
    }));

    render(Settings);
    expect(screen.getByText('Settings')).toBeInTheDocument();
    expect(screen.getByText('Customize your game')).toBeInTheDocument();
  });

  it('should render difficulty options', () => {
    const mockSettings = { showSettings: true };
    const mockTheme = 'light';
    const mockGameConfig = { difficulty: 'EASY' };

    vi.doMock('../lib/stores/gameStore.js', () => ({
      settings: {
        subscribe: vi.fn((callback) => {
          callback(mockSettings);
          return vi.fn();
        }),
        closeSettings: vi.fn()
      },
      theme: {
        subscribe: vi.fn((callback) => {
          callback(mockTheme);
          return vi.fn();
        }),
        toggle: vi.fn()
      },
      gameConfig: {
        subscribe: vi.fn((callback) => {
          callback(mockGameConfig);
          return vi.fn();
        }),
        setDifficulty: vi.fn()
      },
      gameState: {
        newGame: vi.fn()
      }
    }));

    render(Settings);

    expect(screen.getByText('Easy')).toBeInTheDocument();
    expect(screen.getByText('Medium')).toBeInTheDocument();
    expect(screen.getByText('Hard')).toBeInTheDocument();
  });

  it('should render theme toggle', () => {
    const mockSettings = { showSettings: true };
    const mockTheme = 'light';
    const mockGameConfig = { difficulty: 'EASY' };

    vi.doMock('../lib/stores/gameStore.js', () => ({
      settings: {
        subscribe: vi.fn((callback) => {
          callback(mockSettings);
          return vi.fn();
        }),
        closeSettings: vi.fn()
      },
      theme: {
        subscribe: vi.fn((callback) => {
          callback(mockTheme);
          return vi.fn();
        }),
        toggle: vi.fn()
      },
      gameConfig: {
        subscribe: vi.fn((callback) => {
          callback(mockGameConfig);
          return vi.fn();
        }),
        setDifficulty: vi.fn()
      },
      gameState: {
        newGame: vi.fn()
      }
    }));

    render(Settings);

    expect(screen.getByText('Dark theme')).toBeInTheDocument();
    expect(screen.getByText('Enable dark theme for the game')).toBeInTheDocument();
  });
});
