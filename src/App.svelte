<script>
  import { onMount } from "svelte";
  import {
    gameState,
    gameConfig,
    theme,
    timer,
  } from "./lib/stores/gameStore.js";
  import { GAME_STATES } from "./lib/utils/constants.js";
  import Header from "./lib/components/Header.svelte";
  import GameBoard from "./lib/components/GameBoard.svelte";
  import Settings from "./lib/components/Settings.svelte";

  // Initialize game on mount
  onMount(() => {
    gameState.newGame($gameConfig);

    // Start timer when game starts playing
    const unsubscribe = gameState.subscribe((state) => {
      if (state.gameState === GAME_STATES.PLAYING && state.startTime) {
        timer.start();
      } else if (
        state.gameState === GAME_STATES.WON ||
        state.gameState === GAME_STATES.LOST
      ) {
        timer.stop();
      } else if (state.gameState === GAME_STATES.READY) {
        timer.reset();
      }
    });

    return unsubscribe;
  });

  // Apply theme to document
  $: if (typeof document !== "undefined") {
    document.documentElement.setAttribute("data-theme", $theme);
  }
</script>

<main class="app" class:dark={$theme === "dark"}>
  <Header />
  <GameBoard />
  <Settings />
</main>

<style>
  .app {
    min-height: 100vh;
    background: #f5f5f0;
    transition: background-color 0.2s ease;
    padding: 20px 0;
  }

  .app.dark {
    background: #1a1a1a;
  }

  @media (max-width: 768px) {
    .app {
      padding: 10px 0;
    }
  }
</style>
