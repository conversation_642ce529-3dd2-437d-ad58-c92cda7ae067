<script>
  import { onMount } from "svelte";
  import Header from "./lib/Header.svelte";
  import GameBoard from "./lib/GameBoard.svelte";
  import SettingsModal from "./lib/SettingsModal.svelte";
  import {
    isDarkTheme,
    currentDifficulty,
    gameState,
    gameBoard,
    mineCount,
    flagCount,
    revealedCount,
    gameTime,
    isFirstClick,
    showSettings,
  } from "./lib/stores.js";
  import {
    initializeGame,
    placeMines,
    revealCell,
    toggleFlag,
    checkWinCondition,
    countRevealedCells,
    countFlaggedCells,
  } from "./lib/gameLogic.js";

  let gameTimer;
  let currentBoard = [];
  let currentGameState = "ready";
  let currentMineCount = 0;
  let currentIsFirstClick = true;
  let currentShowSettings = false;
  let currentIsDarkTheme = false;
  let currentDifficultySetting = "easy";

  // Subscribe to stores
  gameBoard.subscribe((value) => (currentBoard = value));
  gameState.subscribe((value) => (currentGameState = value));
  mineCount.subscribe((value) => (currentMineCount = value));
  isFirstClick.subscribe((value) => (currentIsFirstClick = value));
  showSettings.subscribe((value) => (currentShowSettings = value));
  isDarkTheme.subscribe((value) => (currentIsDarkTheme = value));
  currentDifficulty.subscribe((value) => (currentDifficultySetting = value));

  onMount(() => {
    // Apply theme on mount
    updateTheme();

    // Initialize game
    initializeNewGame();
  });

  function updateTheme() {
    if (currentIsDarkTheme) {
      document.documentElement.setAttribute("data-theme", "dark");
    } else {
      document.documentElement.removeAttribute("data-theme");
    }
  }

  function initializeNewGame() {
    // Stop any existing timer
    if (gameTimer) {
      clearInterval(gameTimer);
      gameTimer = null;
    }

    // Initialize game
    const gameData = initializeGame(currentDifficultySetting);

    // Update stores
    gameBoard.set(gameData.board);
    mineCount.set(gameData.mineCount);
    flagCount.set(0);
    revealedCount.set(0);
    gameTime.set(0);
    gameState.set("ready");
    isFirstClick.set(true);
  }

  function startTimer() {
    if (gameTimer) return;

    gameTimer = setInterval(() => {
      gameTime.update((time) => time + 1);
    }, 1000);
  }

  function stopTimer() {
    if (gameTimer) {
      clearInterval(gameTimer);
      gameTimer = null;
    }
  }

  function handleCellReveal(event) {
    const { row, col } = event.detail;

    if (currentGameState === "won" || currentGameState === "lost") return;

    // Start timer on first click
    if (currentIsFirstClick) {
      // Place mines avoiding the first clicked cell
      const boardWithMines = placeMines(
        currentBoard.map((row) => row.map((cell) => ({ ...cell }))),
        currentMineCount,
        row,
        col
      );
      gameBoard.set(boardWithMines);

      isFirstClick.set(false);
      gameState.set("playing");
      startTimer();

      // Update currentBoard reference for the reveal logic below
      currentBoard = boardWithMines;
    }

    // Reveal the cell
    const newBoard = revealCell(currentBoard, row, col);
    gameBoard.set(newBoard);

    // Check if mine was hit
    if (newBoard[row][col].isMine) {
      gameState.set("lost");
      stopTimer();
      return;
    }

    // Update revealed count
    const newRevealedCount = countRevealedCells(newBoard);
    revealedCount.set(newRevealedCount);

    // Check win condition
    if (checkWinCondition(newBoard)) {
      gameState.set("won");
      stopTimer();
    }
  }

  function handleCellFlag(event) {
    const { row, col } = event.detail;

    if (currentGameState === "won" || currentGameState === "lost") return;

    // Toggle flag
    const newBoard = toggleFlag(currentBoard, row, col);
    gameBoard.set(newBoard);

    // Update flag count
    const newFlagCount = countFlaggedCells(newBoard);
    flagCount.set(newFlagCount);
  }

  function handleNewGame() {
    initializeNewGame();
  }

  function handleThemeToggle() {
    isDarkTheme.update((value) => !value);
    updateTheme();
  }

  function handleSettingsOpen() {
    showSettings.set(true);
  }

  function handleSettingsClose() {
    showSettings.set(false);
  }

  function handleDifficultyChange(event) {
    const newDifficulty = event.detail;
    currentDifficulty.set(newDifficulty);
    initializeNewGame();
    handleSettingsClose();
  }
</script>

<main class="app">
  <Header
    on:new-game={handleNewGame}
    on:theme-toggle={handleThemeToggle}
    on:settings={handleSettingsOpen}
  />

  <GameBoard
    board={currentBoard}
    gameState={currentGameState}
    on:cell-reveal={handleCellReveal}
    on:cell-flag={handleCellFlag}
  />

  <SettingsModal
    isOpen={currentShowSettings}
    on:close={handleSettingsClose}
    on:theme-toggle={handleThemeToggle}
    on:difficulty-change={handleDifficultyChange}
  />

  <!-- Footer -->
  <footer class="footer">
    <p>
      Made with ❤️ by <a
        href="https://github.com/melvin-chia"
        target="_blank"
        rel="noopener">Melvin Chia</a
      >. Project under MIT License.
    </p>
  </footer>
</main>

<style>
  .app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    gap: 2rem;
  }

  .footer {
    margin-top: auto;
    padding: 1rem;
    text-align: center;
  }

  .footer p {
    margin: 0;
    font-size: 0.875rem;
    color: var(--text-secondary);
  }

  .footer a {
    color: var(--number-2);
    text-decoration: none;
    font-weight: 500;
  }

  .footer a:hover {
    text-decoration: underline;
  }

  @media (max-width: 768px) {
    .app {
      padding: 0.5rem;
      gap: 1.5rem;
    }

    .footer {
      padding: 0.5rem;
    }

    .footer p {
      font-size: 0.75rem;
    }
  }
</style>
